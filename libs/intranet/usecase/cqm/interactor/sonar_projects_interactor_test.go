package interactor_test

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"sa-intranet/usecase/cqm/interactor"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/google/uuid"
	"github.com/samber/do"
)

func TestSonarProjectsInteractorInitialDataComprehensive(t *testing.T) {
	tests := []struct {
		name     string
		filter   string
		page     int
		pageSize int
		wantErr  bool
		verify   func(t *testing.T, got interactor.SonarProjectsData, filter string, page int, pageSize int)
	}{
		{
			name:     "successful data retrieval with filter",
			filter:   "test",
			page:     1,
			pageSize: 10,
			wantErr:  false,
			verify: func(t *testing.T, got interactor.SonarProjectsData, filter string, page int, pageSize int) {
				if got.Pagination.CurrentPage != page {
					t.Errorf("Expected page %d, got %d", page, got.Pagination.CurrentPage)
				}
				if got.Pagination.PerPage != pageSize {
					t.Errorf("Expected pageSize %d, got %d", pageSize, got.Pagination.PerPage)
				}
			},
		},
		{
			name:     "successful data retrieval without filter",
			filter:   "",
			page:     1,
			pageSize: 20,
			wantErr:  false,
			verify: func(t *testing.T, got interactor.SonarProjectsData, filter string, page int, pageSize int) {
				if got.Pagination.CurrentPage != page {
					t.Errorf("Expected page %d, got %d", page, got.Pagination.CurrentPage)
				}
				if got.Pagination.PerPage != pageSize {
					t.Errorf("Expected pageSize %d, got %d", pageSize, got.Pagination.PerPage)
				}
			},
		},
		{
			name:     "large page size",
			filter:   "",
			page:     2,
			pageSize: 100,
			wantErr:  false,
			verify: func(t *testing.T, got interactor.SonarProjectsData, filter string, page int, pageSize int) {
				if got.Pagination.CurrentPage != page {
					t.Errorf("Expected page %d, got %d", page, got.Pagination.CurrentPage)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			inj := injector.Clone()
			usecase := do.MustInvoke[*interactor.SonarProjectsInteractor](inj)

			got, err := usecase.InitialData(tt.filter, tt.page, tt.pageSize)

			if tt.wantErr {
				if err == nil {
					t.Errorf("InitialData() expected error, got nil")
					return
				}
				return
			}

			if err != nil {
				t.Errorf("InitialData() unexpected error = %v", err)
				return
			}

			// Standard verification
			if got.SonarProjects == nil {
				t.Error("Expected SonarProjects to be initialized")
			}

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.filter, tt.page, tt.pageSize)
			}
		})
	}
}

func setupSonarQubeMockServer() *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch {
		case strings.Contains(r.URL.Path, "/api/projects/show"):
			// Extract project key from query parameters
			projectKey := r.URL.Query().Get("project")
			if projectKey == "" {
				projectKey = "test-project"
			}

			response := sonarqube.Project{
				Key:  projectKey,
				Name: "Test SonarQube Project " + projectKey,
			}

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(response)

		case r.URL.Path == "/api/issues/search":
			response := sonarqube.IssuesResponse{
				Total: 5,
				Ps:    10,
				P:     1,
				Issues: []sonarqube.Issue{
					{
						Key:       "test-issue-1",
						Severity:  "MAJOR",
						Message:   "Test issue message",
						Component: "test-component",
						Status:    "OPEN",
					},
				},
			}

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(response)

		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
}

func TestSonarProjectsInteractorCreateSonarProject(t *testing.T) {
	// Setup mock server
	mockServer := setupSonarQubeMockServer()
	defer mockServer.Close()

	// Override SonarQube client configuration to use mock server
	inj := injector.Clone()
	do.Override(inj, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		return sonarqube.ClientConfig{
			URL:   mockServer.URL,
			Token: "test-token",
		}, nil
	})

	// Create test company client and Jira project
	companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](inj)
	companyClient := &model.CompanyClient{
		Name: "Test Company",
	}
	savedClient, err := companyClientRepo.Save(context.Background(), companyClient)
	if err != nil {
		t.Fatalf("Failed to create test company client: %v", err)
	}

	jiraProjectRepo := do.MustInvoke[repository.JiraProjectRepository](inj)
	jiraProject := &model.JiraProject{
		ProjectKey:      "TEST",
		Name:            "Test Jira Project",
		JiraURL:         "https://test.atlassian.net",
		Username:        "<EMAIL>",
		Active:          true,
		Token:           "encrypted-token",
		CompanyClientID: savedClient.ID,
	}
	savedJiraProject, err := jiraProjectRepo.Save(context.Background(), jiraProject)
	if err != nil {
		t.Fatalf("Failed to create test Jira project: %v", err)
	}

	active := true
	tests := []struct {
		name        string
		input       interactor.SonarProjectValidator
		wantErr     bool
		errContains string
		verify      func(t *testing.T, got interactor.SonarProject, input interactor.SonarProjectValidator)
	}{
		{
			name: "successful project creation",
			input: interactor.SonarProjectValidator{
				ProjectKey:    "test-sonar-project",
				JiraProjectID: savedJiraProject.ID,
				Branch:        "main",
				Active:        &active,
			},
			wantErr: false,
			verify: func(t *testing.T, got interactor.SonarProject, input interactor.SonarProjectValidator) {
				if got.ProjectKey != input.ProjectKey {
					t.Errorf("Expected ProjectKey %s, got %s", input.ProjectKey, got.ProjectKey)
				}
				if got.Branch != input.Branch {
					t.Errorf("Expected Branch %s, got %s", input.Branch, got.Branch)
				}
				if got.Active != *input.Active {
					t.Errorf("Expected Active %v, got %v", *input.Active, got.Active)
				}
				if got.ProjectName == "" {
					t.Error("Expected ProjectName to be populated from SonarQube API")
				}
			},
		},
		{
			name: "validation error - missing project key",
			input: interactor.SonarProjectValidator{
				ProjectKey:    "",
				JiraProjectID: savedJiraProject.ID,
				Branch:        "main",
				Active:        &active,
			},
			wantErr:     true,
			errContains: "validation",
		},
		{
			name: "validation error - missing branch",
			input: interactor.SonarProjectValidator{
				ProjectKey:    "test-project",
				JiraProjectID: savedJiraProject.ID,
				Branch:        "",
				Active:        &active,
			},
			wantErr:     true,
			errContains: "validation",
		},
		{
			name: "validation error - missing Jira project ID",
			input: interactor.SonarProjectValidator{
				ProjectKey:    "test-project",
				JiraProjectID: uuid.Nil,
				Branch:        "main",
				Active:        &active,
			},
			wantErr:     true,
			errContains: "validation",
		},
		{
			name: "validation error - missing active flag",
			input: interactor.SonarProjectValidator{
				ProjectKey:    "test-project",
				JiraProjectID: savedJiraProject.ID,
				Branch:        "main",
				Active:        nil,
			},
			wantErr:     true,
			errContains: "validation",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usecase := do.MustInvoke[*interactor.SonarProjectsInteractor](inj)

			got, validationErrors, err := usecase.CreateSonarProject(tt.input)

			if tt.wantErr {
				if err == nil {
					t.Errorf("CreateSonarProject() expected error, got nil")
					return
				}
				if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("CreateSonarProject() error = %v, should contain %v", err, tt.errContains)
				}
				if validationErrors == nil && strings.Contains(tt.errContains, "validation") {
					t.Error("Expected validation errors to be returned")
				}
				return
			}

			if err != nil {
				t.Errorf("CreateSonarProject() unexpected error = %v", err)
				return
			}

			if validationErrors != nil {
				t.Errorf("CreateSonarProject() unexpected validation errors = %v", validationErrors)
				return
			}

			// Standard verification
			if got.ID == uuid.Nil {
				t.Error("Expected ID to be set")
			}
			if got.CreatedAt.IsZero() {
				t.Error("Expected CreatedAt to be set")
			}

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.input)
			}
		})
	}
}

func TestSonarProjectsInteractorUpdateSonarProject(t *testing.T) {
	// Setup mock server
	mockServer := setupSonarQubeMockServer()
	defer mockServer.Close()

	// Override SonarQube client configuration to use mock server
	inj := injector.Clone()
	do.Override(inj, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		return sonarqube.ClientConfig{
			URL:   mockServer.URL,
			Token: "test-token",
		}, nil
	})

	// Create test company client and Jira project
	companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](inj)
	companyClient := &model.CompanyClient{
		Name: "Test Company",
	}
	savedClient, err := companyClientRepo.Save(context.Background(), companyClient)
	if err != nil {
		t.Fatalf("Failed to create test company client: %v", err)
	}

	jiraProjectRepo := do.MustInvoke[repository.JiraProjectRepository](inj)
	jiraProject := &model.JiraProject{
		ProjectKey:      "TEST",
		Name:            "Test Jira Project",
		JiraURL:         "https://test.atlassian.net",
		Username:        "<EMAIL>",
		Active:          true,
		Token:           "encrypted-token",
		CompanyClientID: savedClient.ID,
	}
	savedJiraProject, err := jiraProjectRepo.Save(context.Background(), jiraProject)
	if err != nil {
		t.Fatalf("Failed to create test Jira project: %v", err)
	}

	// Create test SonarQube project
	sonarProjectRepo := do.MustInvoke[repository.SonarqubeProjectRepository](inj)
	sonarProject := &model.SonarqubeProject{
		ProjectKey:    "original-project",
		ProjectName:   "Original Project",
		Branch:        "develop",
		JiraProjectID: savedJiraProject.ID,
		Active:        true,
	}
	savedSonarProject, err := sonarProjectRepo.Save(context.Background(), sonarProject, true)
	if err != nil {
		t.Fatalf("Failed to create test SonarQube project: %v", err)
	}

	active := false
	tests := []struct {
		name        string
		projectID   string
		input       interactor.SonarProjectValidator
		wantErr     bool
		errContains string
		verify      func(t *testing.T, got interactor.SonarProject, input interactor.SonarProjectValidator)
	}{
		{
			name:      "successful project update with same project key",
			projectID: savedSonarProject.ID.String(),
			input: interactor.SonarProjectValidator{
				ProjectKey:    "original-project", // Same as existing
				JiraProjectID: savedJiraProject.ID,
				Branch:        "main",
				Active:        &active,
			},
			wantErr: false,
			verify: func(t *testing.T, got interactor.SonarProject, input interactor.SonarProjectValidator) {
				if got.ProjectKey != input.ProjectKey {
					t.Errorf("Expected ProjectKey %s, got %s", input.ProjectKey, got.ProjectKey)
				}
				if got.Branch != input.Branch {
					t.Errorf("Expected Branch %s, got %s", input.Branch, got.Branch)
				}
				if got.Active != *input.Active {
					t.Errorf("Expected Active %v, got %v", *input.Active, got.Active)
				}
			},
		},
		{
			name:      "successful project update with new project key",
			projectID: savedSonarProject.ID.String(),
			input: interactor.SonarProjectValidator{
				ProjectKey:    "new-project-key", // Different from existing
				JiraProjectID: savedJiraProject.ID,
				Branch:        "feature",
				Active:        &active,
			},
			wantErr: false,
			verify: func(t *testing.T, got interactor.SonarProject, input interactor.SonarProjectValidator) {
				if got.ProjectKey != input.ProjectKey {
					t.Errorf("Expected ProjectKey %s, got %s", input.ProjectKey, got.ProjectKey)
				}
				if got.ProjectName == "" {
					t.Error("Expected ProjectName to be updated from SonarQube API")
				}
			},
		},
		{
			name:        "invalid project ID",
			projectID:   "invalid-uuid",
			input:       interactor.SonarProjectValidator{},
			wantErr:     true,
			errContains: "parse",
		},
		{
			name:      "validation error - missing project key",
			projectID: savedSonarProject.ID.String(),
			input: interactor.SonarProjectValidator{
				ProjectKey:    "",
				JiraProjectID: savedJiraProject.ID,
				Branch:        "main",
				Active:        &active,
			},
			wantErr:     true,
			errContains: "validation",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usecase := do.MustInvoke[*interactor.SonarProjectsInteractor](inj)

			got, validationErrors, err := usecase.UpdateSonarProject(tt.projectID, tt.input)

			if tt.wantErr {
				if err == nil {
					t.Errorf("UpdateSonarProject() expected error, got nil")
					return
				}
				if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("UpdateSonarProject() error = %v, should contain %v", err, tt.errContains)
				}
				return
			}

			if err != nil {
				t.Errorf("UpdateSonarProject() unexpected error = %v", err)
				return
			}

			if validationErrors != nil {
				t.Errorf("UpdateSonarProject() unexpected validation errors = %v", validationErrors)
				return
			}

			// Standard verification
			if got.ID == uuid.Nil {
				t.Error("Expected ID to be set")
			}

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.input)
			}
		})
	}
}
