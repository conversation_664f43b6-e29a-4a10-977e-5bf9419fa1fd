package interactor_test

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"sa-intranet/core"
	"sa-intranet/usecase/cqm/interactor"
	"sa-intranet/usecase/cqm/jira"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/google/uuid"
	"github.com/samber/do"
)

func TestSonarIssuesInteractorInitialDataComprehensive(t *testing.T) {
	// Setup mock servers
	mockSonarServer := setupSonarQubeMockServer()
	defer mockSonarServer.Close()

	// Override SonarQube client configuration
	inj := injector.Clone()
	do.Override(inj, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		return sonarqube.ClientConfig{
			URL:   mockSonarServer.URL,
			Token: "test-token",
		}, nil
	})

	// Create test data
	companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](inj)
	companyClient := &model.CompanyClient{
		Name: "Test Company",
	}
	savedClient, err := companyClientRepo.Save(context.Background(), companyClient)
	if err != nil {
		t.Fatalf("Failed to create test company client: %v", err)
	}

	jiraProjectRepo := do.MustInvoke[repository.JiraProjectRepository](inj)
	jiraProject := &model.JiraProject{
		ProjectKey:      "TEST",
		Name:            "Test Jira Project",
		JiraURL:         "https://test.atlassian.net",
		Username:        "<EMAIL>",
		Active:          true,
		Token:           "encrypted-token",
		CompanyClientID: savedClient.ID,
	}
	savedJiraProject, err := jiraProjectRepo.Save(context.Background(), jiraProject)
	if err != nil {
		t.Fatalf("Failed to create test Jira project: %v", err)
	}

	sonarProjectRepo := do.MustInvoke[repository.SonarqubeProjectRepository](inj)
	sonarProject := &model.SonarqubeProject{
		ProjectKey:    "test-sonar-project",
		ProjectName:   "Test SonarQube Project",
		Branch:        "main",
		JiraProjectID: savedJiraProject.ID,
		Active:        true,
	}
	savedSonarProject, err := sonarProjectRepo.Save(context.Background(), sonarProject, true)
	if err != nil {
		t.Fatalf("Failed to create test SonarQube project: %v", err)
	}

	tests := []struct {
		name      string
		projectID string
		page      int
		pageSize  int
		wantErr   bool
		verify    func(t *testing.T, got interactor.Data, projectID string, page int, pageSize int)
	}{
		{
			name:      "successful data retrieval",
			projectID: savedSonarProject.ID.String(),
			page:      1,
			pageSize:  10,
			wantErr:   false,
			verify: func(t *testing.T, got interactor.Data, projectID string, page int, pageSize int) {
				if got.ProjectID != projectID {
					t.Errorf("Expected ProjectID %s, got %s", projectID, got.ProjectID)
				}
				if got.Pagination.CurrentPage != page {
					t.Errorf("Expected page %d, got %d", page, got.Pagination.CurrentPage)
				}
				if got.Pagination.PerPage != pageSize {
					t.Errorf("Expected pageSize %d, got %d", pageSize, got.Pagination.PerPage)
				}
				if got.JiraProject.ID == uuid.Nil {
					t.Error("Expected JiraProject to be populated")
				}
			},
		},
		{
			name:      "large page size",
			projectID: savedSonarProject.ID.String(),
			page:      2,
			pageSize:  50,
			wantErr:   false,
			verify: func(t *testing.T, got interactor.Data, projectID string, page int, pageSize int) {
				if got.Pagination.CurrentPage != page {
					t.Errorf("Expected page %d, got %d", page, got.Pagination.CurrentPage)
				}
			},
		},
		{
			name:      "invalid project ID",
			projectID: "invalid-uuid",
			page:      1,
			pageSize:  10,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usecase := do.MustInvoke[*interactor.SonarIssuesInteractor](inj)

			got, err := usecase.InitialData(tt.projectID, tt.page, tt.pageSize)

			if tt.wantErr {
				if err == nil {
					t.Errorf("InitialData() expected error, got nil")
					return
				}
				return
			}

			if err != nil {
				t.Errorf("InitialData() unexpected error = %v", err)
				return
			}

			// Standard verification
			if got.SonarIssues == nil {
				t.Error("Expected SonarIssues to be initialized")
			}

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.projectID, tt.page, tt.pageSize)
			}
		})
	}
}

func TestSonarIssuesInteractorGetSonarIssues(t *testing.T) {
	// Setup mock server
	mockServer := setupSonarQubeMockServer()
	defer mockServer.Close()

	// Override SonarQube client configuration
	inj := injector.Clone()
	do.Override(inj, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		return sonarqube.ClientConfig{
			URL:   mockServer.URL,
			Token: "test-token",
		}, nil
	})

	tests := []struct {
		name       string
		projectKey string
		page       int
		perPage    int
		wantErr    bool
		verify     func(t *testing.T, got sonarqube.IssuesResponse, projectKey string, page int, perPage int)
	}{
		{
			name:       "successful issues retrieval",
			projectKey: "test-project",
			page:       1,
			perPage:    10,
			wantErr:    false,
			verify: func(t *testing.T, got sonarqube.IssuesResponse, projectKey string, page int, perPage int) {
				if got.Total == 0 {
					t.Error("Expected some issues to be returned")
				}
				if got.P != page {
					t.Errorf("Expected page %d, got %d", page, got.P)
				}
				if len(got.Issues) == 0 {
					t.Error("Expected issues array to be populated")
				}
			},
		},
		{
			name:       "large page size",
			projectKey: "test-project",
			page:       2,
			perPage:    50,
			wantErr:    false,
			verify: func(t *testing.T, got sonarqube.IssuesResponse, projectKey string, page int, perPage int) {
				if got.P != page {
					t.Errorf("Expected page %d, got %d", page, got.P)
				}
			},
		},
		{
			name:       "empty project key",
			projectKey: "",
			page:       1,
			perPage:    10,
			wantErr:    false, // SonarQube API might handle this gracefully
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usecase := do.MustInvoke[*interactor.SonarIssuesInteractor](inj)

			got, err := usecase.GetSonarIssues(tt.projectKey, tt.page, tt.perPage)

			if tt.wantErr {
				if err == nil {
					t.Errorf("GetSonarIssues() expected error, got nil")
					return
				}
				return
			}

			if err != nil {
				t.Errorf("GetSonarIssues() unexpected error = %v", err)
				return
			}

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.projectKey, tt.page, tt.perPage)
			}
		})
	}
}

func setupCombinedMockServer() *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch {
		// SonarQube API endpoints
		case strings.Contains(r.URL.Path, "/api/projects/show"):
			projectKey := r.URL.Query().Get("project")
			if projectKey == "" {
				projectKey = "test-project"
			}

			response := sonarqube.Project{
				Key:  projectKey,
				Name: "Test SonarQube Project " + projectKey,
			}

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(response)

		case r.URL.Path == "/api/issues/search":
			response := sonarqube.IssuesResponse{
				Total: 5,
				Ps:    10,
				P:     1,
				Issues: []sonarqube.Issue{
					{
						Key:       "test-issue-1",
						Severity:  "MAJOR",
						Message:   "Test issue message",
						Component: "test-component",
						Status:    "OPEN",
						Type:      "BUG",
						Tags:      []string{"security", "performance"},
						Effort:    "30min",
						Author:    "test-author",
					},
				},
			}

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(response)

		// Jira API endpoints
		case strings.Contains(r.URL.Path, "/rest/api/3/project/"):
			projectKey := strings.TrimPrefix(r.URL.Path, "/rest/api/3/project/")

			response := jira.Project{
				ID:   "10001",
				Key:  projectKey,
				Name: "Test Project " + projectKey,
				IssueTypes: []jira.IssueType{
					{
						ID:   "10000",
						Name: "Task",
					},
					{
						ID:   "10001",
						Name: "Technical Debt",
					},
				},
			}

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(response)

		case r.URL.Path == "/rest/api/3/issue":
			response := jira.CreateIssueResponse{
				ID:   "10001",
				Key:  "TEST-123",
				Self: "https://test.atlassian.net/rest/api/3/issue/10001",
			}

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusCreated)
			json.NewEncoder(w).Encode(response)

		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
}

func TestSonarIssuesInteractorCreateJiraIssue(t *testing.T) {
	// Setup mock server
	mockServer := setupCombinedMockServer()
	defer mockServer.Close()

	// Override configurations to use mock server
	inj := injector.Clone()
	do.Override(inj, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		return sonarqube.ClientConfig{
			URL:   mockServer.URL,
			Token: "test-token",
		}, nil
	})
	// Note: jira.ClientConfig is empty struct, no need to override

	// Create test data
	companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](inj)
	companyClient := &model.CompanyClient{
		Name: "Test Company",
	}
	savedClient, err := companyClientRepo.Save(context.Background(), companyClient)
	if err != nil {
		t.Fatalf("Failed to create test company client: %v", err)
	}

	cypher := do.MustInvoke[core.Cypher](inj)
	encryptedToken, err := cypher.Encrypt("test-token")
	if err != nil {
		t.Fatalf("Failed to encrypt token: %v", err)
	}

	jiraProjectRepo := do.MustInvoke[repository.JiraProjectRepository](inj)
	jiraProject := &model.JiraProject{
		ProjectKey:      "TEST",
		Name:            "Test Jira Project",
		JiraURL:         mockServer.URL,
		Username:        "<EMAIL>",
		Active:          true,
		Token:           encryptedToken,
		CompanyClientID: savedClient.ID,
	}
	savedJiraProject, err := jiraProjectRepo.Save(context.Background(), jiraProject)
	if err != nil {
		t.Fatalf("Failed to create test Jira project: %v", err)
	}

	sonarProjectRepo := do.MustInvoke[repository.SonarqubeProjectRepository](inj)
	sonarProject := &model.SonarqubeProject{
		ProjectKey:    "test-sonar-project",
		ProjectName:   "Test SonarQube Project",
		Branch:        "main",
		JiraProjectID: savedJiraProject.ID,
		Active:        true,
	}
	savedSonarProject, err := sonarProjectRepo.Save(context.Background(), sonarProject, true)
	if err != nil {
		t.Fatalf("Failed to create test SonarQube project: %v", err)
	}

	tests := []struct {
		name        string
		projectID   string
		issueKey    string
		wantErr     bool
		errContains string
		verify      func(t *testing.T, got interactor.SonarIssue, projectID string, issueKey string)
	}{
		{
			name:      "successful Jira issue creation",
			projectID: savedSonarProject.ID.String(),
			issueKey:  "test-issue-1",
			wantErr:   false,
			verify: func(t *testing.T, got interactor.SonarIssue, projectID string, issueKey string) {
				if got.Key != issueKey {
					t.Errorf("Expected Key %s, got %s", issueKey, got.Key)
				}
				if got.JiraIssueKey == "" {
					t.Error("Expected JiraIssueKey to be populated")
				}
				if got.URL == "" {
					t.Error("Expected URL to be populated")
				}
				if got.Severity == "" {
					t.Error("Expected Severity to be populated")
				}
			},
		},
		{
			name:        "invalid project ID",
			projectID:   "invalid-uuid",
			issueKey:    "test-issue-1",
			wantErr:     true,
			errContains: "parse",
		},
		{
			name:      "empty issue key",
			projectID: savedSonarProject.ID.String(),
			issueKey:  "",
			wantErr:   true, // Should fail when trying to find the issue
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usecase := do.MustInvoke[*interactor.SonarIssuesInteractor](inj)

			got, err := usecase.CreateJiraIssue(tt.projectID, tt.issueKey)

			if tt.wantErr {
				if err == nil {
					t.Errorf("CreateJiraIssue() expected error, got nil")
					return
				}
				if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("CreateJiraIssue() error = %v, should contain %v", err, tt.errContains)
				}
				return
			}

			if err != nil {
				t.Errorf("CreateJiraIssue() unexpected error = %v", err)
				return
			}

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.projectID, tt.issueKey)
			}
		})
	}
}
